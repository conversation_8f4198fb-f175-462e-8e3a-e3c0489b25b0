import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pbl5/app_common_data/app_text_sytle.dart';
import 'package:pbl5/constants.dart';
import 'package:pbl5/generated/assets.gen.dart';
import 'package:pbl5/shared_customization/widgets/custom_container.dart';
import 'package:pbl5/shared_customization/widgets/custom_widgets/custom_image.dart';
import 'package:pbl5/shared_customization/widgets/texts/custom_text.dart';

class MessageNotificationWidget extends StatelessWidget {
  const MessageNotificationWidget({
    super.key,
    required this.senderName,
    required this.senderAvatar,
    required this.messageContent,
    required this.conversationId,
    required this.onTap,
    this.timestamp,
    this.isRead = false,
  });

  final String senderName;
  final String? senderAvatar;
  final String messageContent;
  final String conversationId;
  final VoidCallback onTap;
  final DateTime? timestamp;
  final bool isRead;

  @override
  Widget build(BuildContext context) {
    return CustomContainer(
      onTap: onTap,
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      padding: EdgeInsets.all(16.w),
      borderRadius: BorderRadius.circular(12.r),
      color: isRead ? Colors.white : kPrimaryColor.withOpacity(0.05),
      border: isRead ? null : Border.all(color: kPrimaryColor.withOpacity(0.2)),
      boxShadow: [
        BoxShadow(
          color: Colors.black.withOpacity(0.1),
          blurRadius: 8,
          offset: const Offset(0, 2),
        ),
      ],
      child: Row(
        children: [
          // Message icon
          CustomContainer(
            width: 50.w,
            height: 50.w,
            borderRadius: BorderRadius.circular(25.r),
            color: kSecondaryColor.withOpacity(0.1),
            child: Icon(
              Icons.message,
              color: kSecondaryColor,
              size: 24.w,
            ),
          ),
          
          SizedBox(width: 12.w),
          
          // Sender avatar
          CustomContainer(
            width: 40.w,
            height: 40.w,
            borderRadius: BorderRadius.circular(20.r),
            clipBehavior: Clip.hardEdge,
            child: CustomImage(
              src: senderAvatar,
              fit: BoxFit.cover,
              placeholder: Assets.images.imgDefaultAvatar.image(
                fit: BoxFit.cover,
              ),
            ),
          ),
          
          SizedBox(width: 12.w),
          
          // Content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: CustomText(
                        senderName,
                        style: AppTextStyle.headingH6,
                        color: Colors.black87,
                        size: 16.sp,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    if (!isRead)
                      CustomContainer(
                        width: 8.w,
                        height: 8.w,
                        borderRadius: BorderRadius.circular(4.r),
                        color: kPrimaryColor,
                      ),
                  ],
                ),
                SizedBox(height: 4.h),
                CustomText(
                  messageContent,
                  style: AppTextStyle.bodyText,
                  color: Colors.black54,
                  size: 14.sp,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                if (timestamp != null) ...[
                  SizedBox(height: 4.h),
                  CustomText(
                    _formatTimestamp(timestamp!),
                    style: AppTextStyle.caption,
                    color: Colors.grey,
                    size: 12.sp,
                  ),
                ],
              ],
            ),
          ),
          
          // Arrow icon
          Icon(
            Icons.arrow_forward_ios,
            color: Colors.grey,
            size: 16.w,
          ),
        ],
      ),
    );
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }
}
