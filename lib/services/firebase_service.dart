import 'dart:convert';
import 'dart:io';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:pbl5/app_common_data/common_data/global_key_variable.dart';
import 'package:pbl5/locator_config.dart';
import 'package:pbl5/services/apis/api_client.dart';

class FirebaseService {
  static final FirebaseMessaging _firebaseMessaging =
      FirebaseMessaging.instance;
  static final FlutterLocalNotificationsPlugin _localNotifications =
      FlutterLocalNotificationsPlugin();
  static final FirebaseAuth _firebaseAuth = FirebaseAuth.instance;
  static final FirebaseDatabase _database = FirebaseDatabase.instance;

  static String? _fcmToken;
  static String? _currentUserId;

  static Future<void> initialize() async {
    // Initialize Firebase
    await Firebase.initializeApp();

    // Initialize local notifications
    await _initializeLocalNotifications();

    // Request permissions
    await _requestPermissions();

    // Get FCM token
    await _getFCMToken();

    // Setup message handlers
    _setupMessageHandlers();
  }

  static Future<void> _initializeLocalNotifications() async {
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    final DarwinInitializationSettings initializationSettingsIOS =
        DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
      onDidReceiveLocalNotification: (id, title, body, payload) async {
        // Handle iOS local notification
      },
    );

    final InitializationSettings initializationSettings =
        InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsIOS,
    );

    await _localNotifications.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: (NotificationResponse response) async {
        // Handle notification tap
        await _handleNotificationTap(response.payload);
      },
    );

    // Create notification channels for Android
    if (Platform.isAndroid) {
      await _createNotificationChannels();
    }
  }

  static Future<void> _createNotificationChannels() async {
    // Match notification channel
    const AndroidNotificationChannel matchChannel = AndroidNotificationChannel(
      'match_channel',
      'Match Notifications',
      description: 'Notifications for new matches',
      importance: Importance.high,
      sound: RawResourceAndroidNotificationSound('notification'),
    );

    // Chat notification channel
    const AndroidNotificationChannel chatChannel = AndroidNotificationChannel(
      'chat_channel',
      'Chat Notifications',
      description: 'Notifications for new messages',
      importance: Importance.high,
      sound: RawResourceAndroidNotificationSound('notification'),
    );

    await _localNotifications
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(matchChannel);

    await _localNotifications
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(chatChannel);
  }

  static Future<void> _requestPermissions() async {
    NotificationSettings settings = await _firebaseMessaging.requestPermission(
      alert: true,
      badge: true,
      sound: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
    );

    debugPrint('FCM Permission granted: ${settings.authorizationStatus}');
  }

  static Future<void> _getFCMToken() async {
    try {
      _fcmToken = await _firebaseMessaging.getToken();
      debugPrint('FCM Token: $_fcmToken');

      if (_fcmToken != null) {
        await _registerFCMToken(_fcmToken!);
      }

      // Listen for token refresh
      _firebaseMessaging.onTokenRefresh.listen((newToken) async {
        _fcmToken = newToken;
        debugPrint('FCM Token refreshed: $newToken');
        await _registerFCMToken(newToken);
      });
    } catch (e) {
      debugPrint('Error getting FCM token: $e');
    }
  }

  static Future<void> _registerFCMToken(String token) async {
    try {
      final apiClient = getIt<ApiClient>();
      await apiClient.registerFCMToken({'fcm_token': token});
      debugPrint('FCM Token registered successfully');
    } catch (e) {
      debugPrint('Error registering FCM token: $e');
    }
  }

  static void _setupMessageHandlers() {
    // Handle foreground messages
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      debugPrint('Received foreground message: ${message.messageId}');
      _handleForegroundMessage(message);
    });

    // Handle background message tap
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      debugPrint('Message opened app: ${message.messageId}');
      _handleMessageOpenedApp(message);
    });

    // Handle app launch from terminated state
    _handleAppLaunchFromNotification();
  }

  static Future<void> _handleForegroundMessage(RemoteMessage message) async {
    final String? type = message.data['type'];

    switch (type) {
      case 'new_match':
        await _showMatchNotification(message);
        break;
      case 'new_message':
        await _showMessageNotification(message);
        break;
      default:
        await _showGenericNotification(message);
    }
  }

  static Future<void> _showMatchNotification(RemoteMessage message) async {
    const AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
      'match_channel',
      'Match Notifications',
      channelDescription: 'Notifications for new matches',
      importance: Importance.high,
      priority: Priority.high,
      icon: '@mipmap/ic_launcher',
      color: Color(0xFFFF9800),
    );

    const DarwinNotificationDetails iOSPlatformChannelSpecifics =
        DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics,
    );

    await _localNotifications.show(
      DateTime.now().millisecondsSinceEpoch.remainder(100000),
      message.notification?.title ?? 'New Match!',
      message.notification?.body ?? 'You have a new match',
      platformChannelSpecifics,
      payload: jsonEncode({
        'type': 'match',
        'data': message.data,
      }),
    );
  }

  static Future<void> _showMessageNotification(RemoteMessage message) async {
    const AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
      'chat_channel',
      'Chat Notifications',
      channelDescription: 'Notifications for new messages',
      importance: Importance.high,
      priority: Priority.high,
      icon: '@mipmap/ic_launcher',
      color: Color(0xFFFF9800),
    );

    const DarwinNotificationDetails iOSPlatformChannelSpecifics =
        DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics,
    );

    await _localNotifications.show(
      DateTime.now().millisecondsSinceEpoch.remainder(100000),
      message.notification?.title ?? 'New Message',
      message.notification?.body ?? 'You have a new message',
      platformChannelSpecifics,
      payload: jsonEncode({
        'type': 'message',
        'data': message.data,
      }),
    );
  }

  static Future<void> _showGenericNotification(RemoteMessage message) async {
    if (message.notification != null) {
      const AndroidNotificationDetails androidPlatformChannelSpecifics =
          AndroidNotificationDetails(
        'default_channel',
        'Default Notifications',
        channelDescription: 'Default notification channel',
        importance: Importance.high,
        priority: Priority.high,
        icon: '@mipmap/ic_launcher',
      );

      const DarwinNotificationDetails iOSPlatformChannelSpecifics =
          DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const NotificationDetails platformChannelSpecifics = NotificationDetails(
        android: androidPlatformChannelSpecifics,
        iOS: iOSPlatformChannelSpecifics,
      );

      await _localNotifications.show(
        DateTime.now().millisecondsSinceEpoch.remainder(100000),
        message.notification!.title,
        message.notification!.body,
        platformChannelSpecifics,
        payload: jsonEncode({
          'type': 'generic',
          'data': message.data,
        }),
      );
    }
  }

  static Future<void> _handleNotificationTap(String? payload) async {
    if (payload != null) {
      try {
        final Map<String, dynamic> data = jsonDecode(payload);
        final String type = data['type'] ?? '';
        final Map<String, dynamic> notificationData = data['data'] ?? {};

        // Navigate based on notification type
        await _navigateBasedOnNotification(type, notificationData);
      } catch (e) {
        debugPrint('Error handling notification tap: $e');
      }
    }
  }

  static Future<void> _handleMessageOpenedApp(RemoteMessage message) async {
    final String? type = message.data['type'];
    await _navigateBasedOnNotification(type ?? '', message.data);
  }

  static Future<void> _handleAppLaunchFromNotification() async {
    RemoteMessage? initialMessage =
        await _firebaseMessaging.getInitialMessage();
    if (initialMessage != null) {
      debugPrint('App launched from notification: ${initialMessage.messageId}');
      final String? type = initialMessage.data['type'];
      await _navigateBasedOnNotification(type ?? '', initialMessage.data);
    }
  }

  static Future<void> _navigateBasedOnNotification(
      String type, Map<String, dynamic> data) async {
    // Get navigator context
    final context = GlobalKeyVariable.navigatorState.currentContext;
    if (context == null) {
      debugPrint('Navigator context is null, cannot navigate');
      return;
    }

    debugPrint('Navigate to: $type with data: $data');

    switch (type) {
      case 'new_match':
      case 'match':
        // Navigate to matches screen or company detail
        final String? matchId = data['match_id'];
        if (matchId != null) {
          debugPrint('Navigating to match with ID: $matchId');
          // TODO: Implement navigation to match/company detail screen
          // Navigator.of(context).pushNamed('/company-detail', arguments: matchId);
        }
        break;
      case 'new_message':
      case 'message':
        // Navigate to chat screen
        final String? conversationId = data['conversation_id'];
        if (conversationId != null) {
          debugPrint(
              'Navigating to chat with conversation ID: $conversationId');
          // TODO: Implement navigation to chat screen
          // Navigator.of(context).pushNamed('/chat', arguments: conversationId);
        }
        break;
      default:
        debugPrint('Unknown notification type: $type');
    }
  }

  static String? get fcmToken => _fcmToken;
  static String? get currentUserId => _currentUserId;

  static Future<void> deleteFCMToken() async {
    try {
      await _firebaseMessaging.deleteToken();
      _fcmToken = null;
      debugPrint('FCM Token deleted');
    } catch (e) {
      debugPrint('Error deleting FCM token: $e');
    }
  }

  // Firebase Authentication methods
  static Future<void> signInWithCustomToken(String customToken) async {
    try {
      final UserCredential userCredential =
          await _firebaseAuth.signInWithCustomToken(customToken);
      _currentUserId = userCredential.user?.uid;
      debugPrint('Firebase Auth successful for user: $_currentUserId');
    } catch (e) {
      debugPrint('Error signing in with custom token: $e');
      throw e;
    }
  }

  static Future<void> signOut() async {
    try {
      await _firebaseAuth.signOut();
      _currentUserId = null;
      debugPrint('Firebase Auth sign out successful');
    } catch (e) {
      debugPrint('Error signing out: $e');
    }
  }

  // Firebase Realtime Database Chat methods
  static Future<void> sendMessage({
    required String conversationId,
    required String senderId,
    required String content,
    String? fileUrl,
    String messageType = 'TEXT',
  }) async {
    try {
      if (_currentUserId == null) {
        throw Exception('User not authenticated with Firebase');
      }

      final DatabaseReference messagesRef =
          _database.ref().child('messages').child(conversationId);

      final String messageId = messagesRef.push().key!;
      final Map<String, dynamic> messageData = {
        'message_id': messageId,
        'sender_id': senderId,
        'content': content,
        'message_type': messageType,
        'file_url': fileUrl,
        'timestamp': DateTime.now().toIso8601String(),
      };

      await messagesRef.child(messageId).set(messageData);

      // Update last message in conversation
      await _updateLastMessage(conversationId, messageData);

      debugPrint('Message sent successfully: $messageId');
    } catch (e) {
      debugPrint('Error sending message: $e');
      throw e;
    }
  }

  static Future<void> _updateLastMessage(
      String conversationId, Map<String, dynamic> messageData) async {
    try {
      final DatabaseReference conversationRef =
          _database.ref().child('chat_rooms').child(conversationId);

      await conversationRef.child('last_message').set(messageData);
    } catch (e) {
      debugPrint('Error updating last message: $e');
    }
  }

  static Stream<List<Map<String, dynamic>>> getMessagesStream(
      String conversationId) {
    return _database
        .ref()
        .child('messages')
        .child(conversationId)
        .orderByChild('timestamp')
        .onValue
        .map((event) {
      final List<Map<String, dynamic>> messages = [];
      if (event.snapshot.value != null) {
        final Map<dynamic, dynamic> data =
            event.snapshot.value as Map<dynamic, dynamic>;
        data.forEach((key, value) {
          if (value is Map) {
            messages.add(Map<String, dynamic>.from(value));
          }
        });
      }
      return messages;
    });
  }

  static Future<void> createOrGetChatRoom(
      String conversationId, List<String> participantIds) async {
    try {
      final DatabaseReference roomRef =
          _database.ref().child('chat_rooms').child(conversationId);

      final snapshot = await roomRef.get();
      if (!snapshot.exists) {
        await roomRef.set({
          'room_id': conversationId,
          'participant_ids': participantIds,
          'created_at': DateTime.now().toIso8601String(),
        });
        debugPrint('Chat room created: $conversationId');
      }
    } catch (e) {
      debugPrint('Error creating chat room: $e');
      throw e;
    }
  }
}

// Background message handler (must be top-level function)
@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp();
  debugPrint('Handling background message: ${message.messageId}');
}
