import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pbl5/app_common_data/app_text_sytle.dart';
import 'package:pbl5/constants.dart';
import 'package:pbl5/generated/assets.gen.dart';
import 'package:pbl5/models/company/company.dart';
import 'package:pbl5/shared_customization/widgets/buttons/custom_button.dart';
import 'package:pbl5/shared_customization/widgets/custom_container.dart';
import 'package:pbl5/shared_customization/widgets/custom_widgets/custom_image.dart';
import 'package:pbl5/shared_customization/widgets/texts/custom_text.dart';

class MatchSuccessDialog extends StatelessWidget {
  const MatchSuccessDialog({
    super.key,
    required this.company,
    required this.onViewProfile,
    required this.onSendMessage,
    required this.onKeepSwiping,
  });

  final Company company;
  final VoidCallback onViewProfile;
  final VoidCallback onSendMessage;
  final VoidCallback onKeepSwiping;

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: CustomContainer(
        padding: EdgeInsets.all(24.w),
        borderRadius: BorderRadius.circular(20.r),
        color: Colors.white,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Match animation/icon
            CustomContainer(
              width: 80.w,
              height: 80.w,
              borderRadius: BorderRadius.circular(40.r),
              color: kPrimaryColor.withOpacity(0.1),
              child: Icon(
                Icons.favorite,
                color: kPrimaryColor,
                size: 40.w,
              ),
            ),
            
            SizedBox(height: 20.h),
            
            // Title
            CustomText(
              "It's a Match! 🎉",
              style: AppTextStyle.headingH4,
              color: kPrimaryColor,
              size: 24.sp,
              textAlign: TextAlign.center,
            ),
            
            SizedBox(height: 12.h),
            
            // Subtitle
            CustomText(
              "You and ${company.companyName} liked each other",
              style: AppTextStyle.bodyText,
              color: Colors.black87,
              size: 16.sp,
              textAlign: TextAlign.center,
            ),
            
            SizedBox(height: 24.h),
            
            // Company info
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // User avatar (placeholder)
                CustomContainer(
                  width: 60.w,
                  height: 60.w,
                  borderRadius: BorderRadius.circular(30.r),
                  clipBehavior: Clip.hardEdge,
                  child: Assets.images.imgDefaultAvatar.image(
                    fit: BoxFit.cover,
                  ),
                ),
                
                SizedBox(width: 20.w),
                
                // Heart icon
                Icon(
                  Icons.favorite,
                  color: kPrimaryColor,
                  size: 30.w,
                ),
                
                SizedBox(width: 20.w),
                
                // Company avatar
                CustomContainer(
                  width: 60.w,
                  height: 60.w,
                  borderRadius: BorderRadius.circular(30.r),
                  clipBehavior: Clip.hardEdge,
                  child: CustomImage(
                    src: company.avatar,
                    fit: BoxFit.cover,
                    placeholder: Assets.images.imgDefaultAvatar.image(
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 24.h),
            
            // Action buttons
            Row(
              children: [
                Expanded(
                  child: CustomButton(
                    onPressed: onViewProfile,
                    text: "View Profile",
                    backgroundColor: Colors.grey.shade200,
                    textColor: Colors.black87,
                    borderRadius: BorderRadius.circular(25.r),
                    height: 50.h,
                  ),
                ),
                
                SizedBox(width: 12.w),
                
                Expanded(
                  child: CustomButton(
                    onPressed: onSendMessage,
                    text: "Send Message",
                    backgroundColor: kPrimaryColor,
                    textColor: Colors.white,
                    borderRadius: BorderRadius.circular(25.r),
                    height: 50.h,
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 12.h),
            
            // Keep swiping button
            CustomButton(
              onPressed: onKeepSwiping,
              text: "Keep Swiping",
              backgroundColor: Colors.transparent,
              textColor: Colors.grey,
              borderRadius: BorderRadius.circular(25.r),
              height: 40.h,
              width: double.infinity,
            ),
          ],
        ),
      ),
    );
  }

  static Future<void> show(
    BuildContext context, {
    required Company company,
    required VoidCallback onViewProfile,
    required VoidCallback onSendMessage,
    required VoidCallback onKeepSwiping,
  }) {
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => MatchSuccessDialog(
        company: company,
        onViewProfile: onViewProfile,
        onSendMessage: onSendMessage,
        onKeepSwiping: onKeepSwiping,
      ),
    );
  }
}
